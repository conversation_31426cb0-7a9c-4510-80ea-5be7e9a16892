using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using System.Web.Http;
using AutoMapper;
using Snp.ePort.Gateway.Api.Infrastructure;
using Snp.ePort.Gateway.Models;
using Snp.ePort.Gateway;

namespace Snp.ePort.Gateway.Api.Controllers
{
    [RoutePrefix("api/msds")]
    public class MSDSController : BaseControllerApi
    {
        private readonly IMSDSGateway _msdsGateway;

        public MSDSController(IMapper mapper, IMSDSGateway msdsGateway) : base(mapper)
        {
            _msdsGateway = msdsGateway;
        }

        /// <summary>
        /// Upload MSDS files for containers
        /// </summary>
        /// <returns>Upload result</returns>
        [HttpPost]
        [Route("upload")]
        public async Task<IHttpActionResult> UploadMSDSFiles()
        {
            try
            {
                var httpRequest = HttpContext.Current.Request;
                
                // Validate request
                if (httpRequest.Files.Count == 0)
                {
                    return BadRequest("Không có file nào được chọn");
                }

                // Get order detail numbers from form data
                var orderDetailNos = new List<string>();
                var orderDetailNosParam = httpRequest.Form["orderDetailNos"];
                if (!string.IsNullOrEmpty(orderDetailNosParam))
                {
                    orderDetailNos = orderDetailNosParam.Split(',').ToList();
                }

                if (orderDetailNos.Count == 0)
                {
                    return BadRequest("OrderDetailNos is required");
                }

                // Create request object with files from HttpRequest
                var request = new MSDSUploadRequest
                {
                    OrderDetailNos = orderDetailNos,
                    Files = new List<HttpPostedFile>()
                };

                // Add files from HttpRequest to request object
                for (int i = 0; i < httpRequest.Files.Count; i++)
                {
                    request.Files.Add(httpRequest.Files[i]);
                }

                var result = await _msdsGateway.UploadMSDSFilesAsync(request);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result.Message);
                }
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        /// <summary>
        /// Delete MSDS file
        /// </summary>
        /// <param name="fileId">File ID to delete</param>
        /// <returns>Delete result</returns>
        [HttpDelete]
        [Route("delete/{fileId}")]
        public async Task<IHttpActionResult> DeleteMSDSFile(string fileId)
        {
            try
            {
                if (string.IsNullOrEmpty(fileId))
                {
                    return BadRequest("FileId is required");
                }

                var result = await _msdsGateway.DeleteMSDSFileAsync(fileId);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result.Message);
                }
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        /// <summary>
        /// Get MSDS files by order detail number
        /// </summary>
        /// <param name="orderDetailNo">Order detail number</param>
        /// <returns>List of MSDS files</returns>
        [HttpGet]
        [Route("files/{orderDetailNo}")]
        public async Task<IHttpActionResult> GetMSDSFiles(string orderDetailNo)
        {
            try
            {
                if (string.IsNullOrEmpty(orderDetailNo))
                {
                    return BadRequest("OrderDetailNo is required");
                }

                var files = await _msdsGateway.GetMSDSFilesByOrderDetailNoAsync(orderDetailNo);
                return Ok(files);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        /// <summary>
        /// Get MSDS configuration
        /// </summary>
        /// <returns>MSDS configuration</returns>
        [HttpGet]
        [Route("config")]
        public async Task<IHttpActionResult> GetMSDSConfig()
        {
            try
            {
                var config = await _msdsGateway.GetMSDSConfigAsync();
                return Ok(config);
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }
    }
}
