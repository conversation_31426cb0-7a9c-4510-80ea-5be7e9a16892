﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{0AFAF605-C505-4C78-8E24-4F9F84E2F462}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Snp.ePort.Gateway.Impl</RootNamespace>
    <AssemblyName>Snp.ePort.Gateway.Impl</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <UseVSHostingProcess>false</UseVSHostingProcess>
    <CodeAnalysisRuleSet>Snp.ePort.Gateway.Impl.ruleset</CodeAnalysisRuleSet>
    <RunCodeAnalysis>true</RunCodeAnalysis>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>Snp.ePort.Gateway.Impl.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Autofac, Version=*******, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Autofac.4.6.2\lib\net45\Autofac.dll</HintPath>
    </Reference>
    <Reference Include="Autofac.Extras.DynamicProxy, Version=*******, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Autofac.Extras.DynamicProxy.4.5.0\lib\net45\Autofac.Extras.DynamicProxy.dll</HintPath>
    </Reference>
    <Reference Include="Autofac.Integration.Wcf, Version=4.0.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Autofac.Wcf.4.0.0\lib\net45\Autofac.Integration.Wcf.dll</HintPath>
    </Reference>
    <Reference Include="Autofac.Multitenant, Version=4.2.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Autofac.Multitenant.4.2.0\lib\net451\Autofac.Multitenant.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper, Version=6.2.1.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\..\packages\AutoMapper.6.2.1\lib\net45\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="AutoMapper.EF6, Version=1.1.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\AutoMapper.EF6.1.1.1\lib\net45\AutoMapper.EF6.dll</HintPath>
    </Reference>
    <Reference Include="Castle.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=407dd0808d44fbdc, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Castle.Core.4.3.1\lib\net45\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="DelegateDecompiler, Version=1.0.0.0, Culture=neutral, PublicKeyToken=93b26a10a04705bd, processorArchitecture=MSIL">
      <HintPath>..\..\packages\DelegateDecompiler.0.23.0\lib\net40-client\DelegateDecompiler.dll</HintPath>
    </Reference>
    <Reference Include="DelegateDecompiler.EntityFramework, Version=1.0.0.0, Culture=neutral, PublicKeyToken=93b26a10a04705bd, processorArchitecture=MSIL">
      <HintPath>..\..\packages\DelegateDecompiler.EntityFramework.0.23.0\lib\net45\DelegateDecompiler.EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Office.v22.2.Core, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.RichEdit.v22.2.Core, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.RichEdit.v22.2.Export, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Drawing.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Printing.v22.2.Core, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Charts.v22.2.Core, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraCharts.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraReports.v22.2, Version=22.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\..\packages\EntityFramework.6.1.3\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Mono.Reflection, Version=1.1.0.0, Culture=neutral, PublicKeyToken=0738eb9f132ed756, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Mono.Reflection.1.1.0.0\lib\Mono.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Newtonsoft.Json.12.0.2\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Runtime.InteropServices.RuntimeInformation.4.0.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AccountType\AccountTypeGateway.cs" />
    <Compile Include="AuthGateway.cs" />
    <Compile Include="BillingGateway.cs" />
    <Compile Include="Business\RegisteredContainerDeliveryGateway.cs" />
    <Compile Include="Business\RegisteredShippingGateway.cs" />
    <Compile Include="CacheHelper.cs" />
    <Compile Include="ClientFactory.cs" />
    <Compile Include="Common\ActivityLogGateway.cs" />
    <Compile Include="Common\EmailSenderGateway.cs" />
    <Compile Include="Common\OperMethodGateway.cs" />
    <Compile Include="CategoryGateway.cs" />
    <Compile Include="AnonymouseGateway.cs" />
    <Compile Include="ConfigGateway.cs" />
    <Compile Include="ContInfoGateway.cs" />
    <Compile Include="CustomsGateway.cs" />
    <Compile Include="EportBillingGateway.cs" />
    <Compile Include="Intf\EPortCustomClearanceGateway.cs" />
    <Compile Include="Intf\EPort2PSafeGateway.cs" />
    <Compile Include="RegistrationGateway.cs" />
    <Compile Include="Service References\EInvoiceGateway\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\EportBillingService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\FtpServiceGateway\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\GetVoyageOTM2TOPO\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\InvoiceWrapperService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\LoyalService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\LoyaltyIntegrationService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\LoyaltyUpdateInvoiceService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\NotifyGateway\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\Otm2ePort24hService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\PSafe2ePortViolationService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\SmsNotificationGateway\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\SnpDigitalSignService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\TOS.EportIntfService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\TosCustomsIntergrationService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\UpdateVoyageTOPO2OTM\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\WcfContInfoOtm\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="SettingGateway.cs" />
    <Compile Include="Settings.cs" />
    <Compile Include="SmsGateway.cs" />
    <Compile Include="Intf\OrderIntfGateway.cs" />
    <Compile Include="OrderGateway.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ContMovementsGateway.cs" />
    <Compile Include="SnpTransactionGateway.cs" />
    <Compile Include="TOSGateway.cs" />
    <Compile Include="TOSContainerGateway.cs" />
    <Compile Include="TOSCategoryGateway.cs" />
    <Compile Include="TOSOperationGateway.cs" />
    <Compile Include="TOSVesselGateway.cs" />
    <Compile Include="AccompaniedService\AccompaniedServiceGateway.cs" />
    <Compile Include="Transport\TransportGateway.cs" />
    <Compile Include="MSDSGateway.cs" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="Properties\DataSources\Snp.ePort.Entity.CHARGE.datasource" />
    <None Include="Properties\DataSources\Snp.ePort.Entity.DTO.CHARGE_DTO.datasource" />
    <None Include="Properties\DataSources\Snp.ePort.Entity.DTO.VesVoyagesLlPodChangeDetailDTO.datasource" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway1.wsdl" />
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway21.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway210.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway211.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway212.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway213.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway214.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway215.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway22.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway23.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway24.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway25.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway26.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway27.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway28.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway29.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceServiceBE.wsdl" />
    <None Include="Service References\EInvoiceGateway\EInvoiceServiceBE1.wsdl" />
    <None Include="Service References\EInvoiceGateway\Snp.ePort.Gateway.Impl.EInvoiceGateway.InvoiceWsCfg.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\EInvoiceGateway\Snp.ePort.Gateway.Impl.EInvoiceGateway.Item.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\EInvoiceGateway\Snp.ePort.Gateway.Impl.EInvoiceGateway.UploadInvAttachmentFkeyInStreamResponse1.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\EportBillingService\EportBillingService.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EportBillingService\EportBillingService1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EportBillingService\EportBillingService2.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EportBillingService\EportBillingService3.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EportBillingService\EportBillingService4.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EportBillingService\EportBillingService5.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\EportBillingService\EportBillingServiceBE.wsdl" />
    <None Include="Service References\FtpServiceGateway\FTPServiceGateway.wsdl" />
    <None Include="Service References\FtpServiceGateway\FTPServiceGateway.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\FtpServiceGateway\FTPServiceGateway1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\FtpServiceGateway\FTPServiceGateway2.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\FtpServiceGateway\FTPServiceGateway3.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\FtpServiceGateway\FTPServiceGateway4.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\FtpServiceGateway\FTPServiceGatewayBE.wsdl" />
    <None Include="Service References\GetVoyageOTM2TOPO\GetVoyageOTM2TOPO.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\GetVoyageOTM2TOPO\VoyageProcess.wsdl" />
    <None Include="Service References\InvoiceWrapperService\InvoiceWrapperService.wsdl" />
    <None Include="Service References\InvoiceWrapperService\InvoiceWrapperService.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\InvoiceWrapperService\InvoiceWrapperService1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\InvoiceWrapperService\InvoiceWrapperService2.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\InvoiceWrapperService\InvoiceWrapperService3.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\InvoiceWrapperService\InvoiceWrapperService4.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\InvoiceWrapperService\Snp.ePort.Gateway.Impl.InvoiceWrapperService.ImportingModelResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\InvoiceWrapperService\Snp.ePort.Gateway.Impl.InvoiceWrapperService.InvoiceInfoModel.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\InvoiceWrapperService\Snp.ePort.Gateway.Impl.InvoiceWrapperService.InvoiceModel.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\InvoiceWrapperService\Snp.ePort.Gateway.Impl.InvoiceWrapperService.MessResponseModel.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\InvoiceWrapperService\Snp.ePort.Gateway.Impl.InvoiceWrapperService.PointManagementCustomerTransactionHistoryDetailModel.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\InvoiceWrapperService\Snp.ePort.Gateway.Impl.InvoiceWrapperService.ServiceModel.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\LoyalService\IntegrationWrapperService.wsdl" />
    <None Include="Service References\LoyalService\IntegrationWrapperService.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\LoyalService\IntegrationWrapperService1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\LoyalService\IntegrationWrapperService2.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\LoyalService\IntegrationWrapperService3.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\LoyalService\IntegrationWrapperService4.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\LoyaltyIntegrationService\GetInvoicesFromKHTTsvc.wsdl" />
    <None Include="Service References\LoyaltyUpdateInvoiceService\UpdateInvoiceWrapper.wsdl" />
    <None Include="Service References\LoyaltyUpdateInvoiceService\UpdateInvoiceWrapper.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\LoyaltyUpdateInvoiceService\UpdateInvoiceWrapper1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\NotifyGateway\NotifyGateway1.wsdl" />
    <None Include="Service References\NotifyGateway\NotifyGateway42.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\NotifyGateway\NotifyGateway43.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\NotifyGateway\NotifyGateway44.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\NotifyGateway\NotifyGateway45.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\Otm2ePort24hService\BPELProcessExtended.wsdl" />
    <None Include="Service References\Otm2ePort24hService\getcontainerstatusext.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\PSafe2ePortViolationService\IViolationInfoService.wsdl" />
    <None Include="Service References\SmsNotificationGateway\SmsGateway.wsdl" />
    <None Include="Service References\SmsNotificationGateway\SmsGateway.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\SmsNotificationGateway\SmsGateway1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\SnpDigitalSignService\SnpDigitalSignBE.wsdl" />
    <None Include="Service References\SnpDigitalSignService\SnpDigitalSignServices.wsdl" />
    <None Include="Service References\SnpDigitalSignService\SnpDigitalSignServices.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\SnpDigitalSignService\SnpDigitalSignServices1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\SnpDigitalSignService\SnpDigitalSignServices2.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\SnpDigitalSignService\SnpDigitalSignServices3.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\SnpDigitalSignService\SnpDigitalSignServices4.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService13.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService14.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService15.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService19.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService2.wsdl" />
    <None Include="Service References\TOS.EportIntfService\EportIntfService20.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService21.wsdl" />
    <None Include="Service References\TOS.EportIntfService\EportIntfService21.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService211.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService212.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService3.wsdl" />
    <None Include="Service References\TOS.EportIntfService\EportIntfService30.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService32.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService33.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService34.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService4.wsdl" />
    <None Include="Service References\TOS.EportIntfService\EportIntfService4.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService214.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService215.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService216.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService217.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService218.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService219.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService24.wsdl" />
    <None Include="Service References\TOS.EportIntfService\EportIntfService25.wsdl" />
    <None Include="Service References\TOS.EportIntfService\EportIntfService91.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService2.wsdl" />
    <None Include="Service References\TOS.EportIntfService\EportIntfService2.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService3.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService94.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService95.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService96.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService97.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TOS.EportIntfService\EportIntfService98.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TosCustomsIntergrationService\Snp.ePort.Gateway.Impl.TosCustomsIntergrationService.CustomerCustomsDeclaration.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\TosCustomsIntergrationService\Snp.ePort.Gateway.Impl.TosCustomsIntergrationService.CustomerCustomsDeclaration.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\TosCustomsIntergrationService\Snp.ePort.Gateway.Impl.TosCustomsIntergrationService.ViewCustoms39Container.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\TosCustomsIntergrationService\TosCustomsIntergrationBE1.wsdl" />
    <None Include="Service References\TosCustomsIntergrationService\TosCustomsIntergrationService.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TosCustomsIntergrationService\TosCustomsIntergrationService1.wsdl" />
    <None Include="Service References\TosCustomsIntergrationService\TosCustomsIntergrationService1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\TosCustomsIntergrationService\TosCustomsIntergrationService2.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\UpdateVoyageTOPO2OTM\Snp.ePort.Gateway.Impl.UpdateVoyageTOPO2OTM.UpdateVoyageToOTMResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\UpdateVoyageTOPO2OTM\Snp.ePort.Gateway.Impl.UpdateVoyageTOPO2OTM.VoyageResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\UpdateVoyageTOPO2OTM\UpdateVoyageTOPO2OTM.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\UpdateVoyageTOPO2OTM\VoyageTOPO2OTMProcess.wsdl" />
    <None Include="Service References\WcfContInfoOtm\GetContainerOTM2Eport.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\WcfContInfoOtm\OTMContainerBPELProcess.wsdl" />
    <None Include="Service References\WcfContInfoOtm\Snp.ePort.Gateway.Impl.WcfContInfoOtm.containerDetailListContainerDetail.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\WcfContInfoOtm\Snp.ePort.Gateway.Impl.WcfContInfoOtm.processResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Snp.ePort.Gateway.Impl.ruleset" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Snp.ePort.Core\Snp.ePort.Core.csproj">
      <Project>{ff3110c1-eea8-4f5b-a448-3bde77cd87ca}</Project>
      <Name>Snp.ePort.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Gateway\Snp.ePort.Gateway.csproj">
      <Project>{9f1b7a5a-c632-4917-8743-070ff43bf846}</Project>
      <Name>Snp.ePort.Gateway</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Gateway.Api\Snp.ePort.Gateway.Api.csproj">
      <Project>{B1642025-17FC-4300-9C7B-655E427BE90C}</Project>
      <n>Snp.ePort.Gateway.Api</n>
    </ProjectReference>
    <ProjectReference Include="..\Snp.Log\Snp.Log.csproj">
      <Project>{4CB088CD-F669-4176-869C-95EB27348845}</Project>
      <Name>Snp.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Entity\Snp.ePort.Entity.csproj">
      <Project>{910c50b0-22e2-415c-97de-435bb67a47e1}</Project>
      <Name>Snp.ePort.Entity</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Report\Snp.ePort.Report.csproj">
      <Project>{28EE7921-F456-4D13-A802-B5959C4EAC84}</Project>
      <Name>Snp.ePort.Report</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Repository\Snp.ePort.Repository.csproj">
      <Project>{CEB0D04B-4E02-4BC9-A988-E937892459E9}</Project>
      <Name>Snp.ePort.Repository</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Service\Snp.ePort.Service.csproj">
      <Project>{ef17ac6e-fcab-4c99-9998-d87cccf81641}</Project>
      <Name>Snp.ePort.Service</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Common\Snp.ePort.Common.csproj">
      <Project>{f58832de-5553-42c9-8566-8120535248a8}</Project>
      <Name>Snp.ePort.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.Tos.Entity\Snp.Tos.Entity.csproj">
      <Project>{62d4bfe6-7de9-44ec-a16d-f15a2a894235}</Project>
      <Name>Snp.Tos.Entity</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.Tos.Repository\Snp.Tos.Repository.csproj">
      <Project>{90F21576-7149-43E7-B92D-AE4A222C335C}</Project>
      <Name>Snp.Tos.Repository</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.Tos.Service\Snp.Tos.Service.csproj">
      <Project>{7d061055-aabe-417f-ba73-511f993b261b}</Project>
      <Name>Snp.Tos.Service</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadataStorage Include="Service References\EInvoiceGateway\" />
    <WCFMetadataStorage Include="Service References\EportBillingService\" />
    <WCFMetadataStorage Include="Service References\FtpServiceGateway\" />
    <WCFMetadataStorage Include="Service References\GetVoyageOTM2TOPO\" />
    <WCFMetadataStorage Include="Service References\InvoiceWrapperService\" />
    <WCFMetadataStorage Include="Service References\LoyalService\" />
    <WCFMetadataStorage Include="Service References\LoyaltyIntegrationService\" />
    <WCFMetadataStorage Include="Service References\LoyaltyUpdateInvoiceService\" />
    <WCFMetadataStorage Include="Service References\NotifyGateway\" />
    <WCFMetadataStorage Include="Service References\Otm2ePort24hService\" />
    <WCFMetadataStorage Include="Service References\PSafe2ePortViolationService\" />
    <WCFMetadataStorage Include="Service References\SmsNotificationGateway\" />
    <WCFMetadataStorage Include="Service References\SnpDigitalSignService\" />
    <WCFMetadataStorage Include="Service References\TOS.EportIntfService\" />
    <WCFMetadataStorage Include="Service References\TOS.EportIntfService\" />
    <WCFMetadataStorage Include="Service References\TosCustomsIntergrationService\" />
    <WCFMetadataStorage Include="Service References\UpdateVoyageTOPO2OTM\" />
    <WCFMetadataStorage Include="Service References\WcfContInfoOtm\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\EInvoiceGateway\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\EInvoiceGateway\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\EInvoiceGateway\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\WcfContInfoOtm\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\WcfContInfoOtm\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\WcfContInfoOtm\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\TosCustomsIntergrationService\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\TosCustomsIntergrationService\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\TosCustomsIntergrationService\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\LoyaltyUpdateInvoiceService\UpdateInvoiceWrapper.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\LoyaltyUpdateInvoiceService\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\LoyaltyUpdateInvoiceService\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\LoyaltyUpdateInvoiceService\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\InvoiceWrapperService\InvoiceWrapperService.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\InvoiceWrapperService\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\InvoiceWrapperService\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\InvoiceWrapperService\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\UpdateVoyageTOPO2OTM\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\UpdateVoyageTOPO2OTM\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\UpdateVoyageTOPO2OTM\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\LoyaltyIntegrationService\GetInvoicesFromKHTTsvc.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\LoyaltyIntegrationService\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\LoyaltyIntegrationService\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\LoyaltyIntegrationService\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\FtpServiceGateway\FTPServiceGateway.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\FtpServiceGateway\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\FtpServiceGateway\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\FtpServiceGateway\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\LoyalService\IntegrationWrapperService.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\LoyalService\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\LoyalService\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\LoyalService\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\EportBillingService\EportBillingService.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\EportBillingService\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\EportBillingService\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\EportBillingService\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\GetVoyageOTM2TOPO\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\GetVoyageOTM2TOPO\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\GetVoyageOTM2TOPO\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\NotifyGateway\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\NotifyGateway\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\NotifyGateway\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\PSafe2ePortViolationService\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\PSafe2ePortViolationService\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\PSafe2ePortViolationService\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\NotifyGateway\NotifyGateway1.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\Otm2ePort24hService\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\Otm2ePort24hService\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\Otm2ePort24hService\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\SmsNotificationGateway\SmsGateway.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\SmsNotificationGateway\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\SmsNotificationGateway\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\SmsNotificationGateway\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\TosCustomsIntergrationService\TosCustomsIntergrationService1.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\TOS.EportIntfService\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\TOS.EportIntfService\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\TOS.EportIntfService\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\TOS.EportIntfService\EportIntfService1.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\SnpDigitalSignService\SnpDigitalSignServices.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\SnpDigitalSignService\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\SnpDigitalSignService\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\SnpDigitalSignService\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\EInvoiceGateway\EInvoiceGateway1.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\TOS.EportIntfService\EportIntfService4.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\TOS.EportIntfService\EportIntfService2.disco" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>