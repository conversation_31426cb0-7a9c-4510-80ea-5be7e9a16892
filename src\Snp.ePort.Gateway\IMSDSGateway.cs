using System.Collections.Generic;
using System.Threading.Tasks;
using Snp.ePort.Gateway.Models;

namespace Snp.ePort.Gateway
{
    public interface IMSDSGateway
    {
        /// <summary>
        /// Upload MSDS files to MinIO and save metadata to database
        /// </summary>
        /// <param name="request">Upload request containing files and order detail numbers</param>
        /// <returns>Upload response with file information</returns>
        Task<MSDSUploadResponse> UploadMSDSFilesAsync(MSDSUploadRequest request);

        /// <summary>
        /// Delete MSDS file from MinIO and database
        /// </summary>
        /// <param name="fileId">File ID to delete</param>
        /// <returns>Delete response</returns>
        Task<MSDSDeleteResponse> DeleteMSDSFileAsync(string fileId);

        /// <summary>
        /// Get MSDS files by order detail number
        /// </summary>
        /// <param name="orderDetailNo">Order detail number</param>
        /// <returns>List of MSDS files</returns>
        Task<List<MSDSFileInfo>> GetMSDSFilesByOrderDetailNoAsync(string orderDetailNo);

        /// <summary>
        /// Get MSDS configuration from TOS service
        /// </summary>
        /// <returns>MSDS configuration</returns>
        Task<MSDSConfigInfo> GetMSDSConfigAsync();
    }
}
