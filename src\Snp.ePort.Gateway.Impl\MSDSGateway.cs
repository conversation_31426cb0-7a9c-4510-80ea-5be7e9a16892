using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Snp.ePort.Gateway.Models;
using Snp.ePort.Gateway.Api.Infrastructure;
using Snp.ePort.Gateway.Api.Services;
using Snp.ePort.Common;

namespace Snp.ePort.Gateway.Impl
{
    public class MSDSGateway : IMSDSGateway
    {
        private readonly IMinIOService _minIOService;
        private readonly IEportService _eportService;
        private readonly ITOSService _tosService;

        public MSDSGateway(IMinIOService minIOService, IEportService eportService, ITOSService tosService)
        {
            _minIOService = minIOService;
            _eportService = eportService;
            _tosService = tosService;
        }

        public async Task<MSDSUploadResponse> UploadMSDSFilesAsync(MSDSUploadRequest request)
        {
            var response = new MSDSUploadResponse();
            
            try
            {
                // Get MSDS configuration from TOS service
                var config = await GetMSDSConfigAsync();
                
                // Validate files
                var validationResult = ValidateFiles(request.Files, config);
                if (!validationResult.IsValid)
                {
                    response.Success = false;
                    response.Message = validationResult.ErrorMessage;
                    return response;
                }

                var uploadedFiles = new List<MSDSUploadedFileInfo>();

                foreach (var file in request.Files)
                {
                    // Generate unique file name
                    var fileId = Guid.NewGuid().ToString();
                    var fileName = file.FileName;
                    var fileExtension = Path.GetExtension(fileName);
                    var uniqueFileName = $"{fileId}{fileExtension}";
                    var filePath = $"msds/{DateTime.Now.Year}/{uniqueFileName}";

                    // Upload to MinIO
                    var uploadResult = await _minIOService.UploadFileAsync(filePath, file.InputStream, file.ContentType);
                    
                    if (uploadResult.Success)
                    {
                        // Save metadata to database via ePort service
                        var fileMetadata = new MSDSFileMetadata
                        {
                            FileId = fileId,
                            FileName = fileName,
                            FilePath = filePath,
                            FileSize = file.ContentLength,
                            ContentType = file.ContentType,
                            OrderDetailNos = request.OrderDetailNos
                        };

                        var saveResult = await _eportService.SaveMSDSFileMetadataAsync(fileMetadata);
                        
                        if (saveResult.Success)
                        {
                            uploadedFiles.Add(new MSDSUploadedFileInfo
                            {
                                FileId = fileId,
                                FileName = fileName,
                                FilePath = filePath,
                                FileSize = file.ContentLength,
                                LinkedOrderDetails = request.OrderDetailNos
                            });
                        }
                        else
                        {
                            // Rollback: delete from MinIO if database save failed
                            await _minIOService.DeleteFileAsync(filePath);
                        }
                    }
                }

                response.Success = uploadedFiles.Count > 0;
                response.Message = response.Success ? 
                    $"Upload thành công {uploadedFiles.Count} file(s)" : 
                    "Không có file nào được upload thành công";
                response.UploadedFiles = uploadedFiles;
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"Lỗi khi upload file: {ex.Message}";
            }

            return response;
        }

        public async Task<MSDSDeleteResponse> DeleteMSDSFileAsync(string fileId)
        {
            var response = new MSDSDeleteResponse();
            
            try
            {
                // Get file info from database
                var fileInfo = await _eportService.GetMSDSFileByIdAsync(fileId);
                
                if (fileInfo == null)
                {
                    response.Success = false;
                    response.Message = "File không tồn tại";
                    return response;
                }

                // Delete from MinIO
                var deleteResult = await _minIOService.DeleteFileAsync(fileInfo.FilePath);
                
                if (deleteResult.Success)
                {
                    // Delete from database
                    var dbDeleteResult = await _eportService.DeleteMSDSFileAsync(fileId);
                    
                    response.Success = dbDeleteResult.Success;
                    response.Message = dbDeleteResult.Success ? 
                        "Xóa file thành công" : 
                        "Xóa file khỏi database thất bại";
                }
                else
                {
                    response.Success = false;
                    response.Message = "Xóa file khỏi storage thất bại";
                }
            }
            catch (Exception ex)
            {
                response.Success = false;
                response.Message = $"Lỗi khi xóa file: {ex.Message}";
            }

            return response;
        }

        public async Task<List<MSDSFileInfo>> GetMSDSFilesByOrderDetailNoAsync(string orderDetailNo)
        {
            try
            {
                return await _eportService.GetMSDSFilesByOrderDetailNoAsync(orderDetailNo);
            }
            catch (Exception)
            {
                return new List<MSDSFileInfo>();
            }
        }

        private ValidationResult ValidateFiles(List<System.Web.HttpPostedFile> files, MSDSConfigInfo config)
        {
            if (files == null || files.Count == 0)
            {
                return new ValidationResult { IsValid = false, ErrorMessage = "Không có file nào được chọn" };
            }

            if (files.Count > config.MaxFilesPerContainer)
            {
                return new ValidationResult { IsValid = false, ErrorMessage = $"Chỉ được upload tối đa {config.MaxFilesPerContainer} file" };
            }

            var totalSize = files.Sum(f => f.ContentLength);
            if (totalSize > config.MaxTotalSizePerContainer)
            {
                return new ValidationResult { IsValid = false, ErrorMessage = $"Tổng dung lượng file vượt quá {config.MaxTotalSizePerContainer / 1024 / 1024}MB" };
            }

            foreach (var file in files)
            {
                var extension = Path.GetExtension(file.FileName).ToLower();
                if (!config.AllowedExtensions.Contains(extension))
                {
                    return new ValidationResult { IsValid = false, ErrorMessage = $"File {file.FileName} có định dạng không được hỗ trợ" };
                }
            }

            return new ValidationResult { IsValid = true };
        }
    }

    public class ValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
    }

    public class MSDSFileMetadata
    {
        public string FileId { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public string ContentType { get; set; }
        public List<string> OrderDetailNos { get; set; }
    }
}
