using System.Collections.Generic;
using System.Threading.Tasks;
using Snp.ePort.Gateway.Models;

namespace Snp.ePort.Gateway.Api.Infrastructure
{
    public interface IEportService
    {
        /// <summary>
        /// Save MSDS file metadata to database
        /// </summary>
        /// <param name="fileMetadata">File metadata</param>
        /// <returns>Save result</returns>
        Task<EportServiceResult> SaveMSDSFileMetadataAsync(MSDSFileMetadata fileMetadata);

        /// <summary>
        /// Delete MSDS file metadata from database
        /// </summary>
        /// <param name="fileId">File ID</param>
        /// <returns>Delete result</returns>
        Task<EportServiceResult> DeleteMSDSFileAsync(string fileId);

        /// <summary>
        /// Get MSDS file by ID
        /// </summary>
        /// <param name="fileId">File ID</param>
        /// <returns>File info</returns>
        Task<MSDSFileInfo> GetMSDSFileByIdAsync(string fileId);

        /// <summary>
        /// Get MSDS files by order detail number
        /// </summary>
        /// <param name="orderDetailNo">Order detail number</param>
        /// <returns>List of MSDS files</returns>
        Task<List<MSDSFileInfo>> GetMSDSFilesByOrderDetailNoAsync(string orderDetailNo);
    }

    public class EportServiceResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
    }
}
