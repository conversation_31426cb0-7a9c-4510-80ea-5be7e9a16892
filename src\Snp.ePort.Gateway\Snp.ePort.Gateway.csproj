﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9F1B7A5A-C632-4917-8743-070FF43BF846}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Snp.ePort.Gateway</RootNamespace>
    <AssemblyName>Snp.ePort.Gateway</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <SkipPostSharp>True</SkipPostSharp>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <UseVSHostingProcess>false</UseVSHostingProcess>
    <CodeAnalysisRuleSet>Snp.ePort.Gateway.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>Snp.ePort.Gateway.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Drawing.v22.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v22.2.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.v22.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpo.v22.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Web" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AccountType\IAccountTypeGateway.cs" />
    <Compile Include="Business\IRegisteredContainerDeliveryGateway.cs" />
    <Compile Include="Business\IRegisteredShippingGateway.cs" />
    <Compile Include="Common\IActivityLogGateway.cs" />
    <Compile Include="Common\IOperMethodGateway.cs" />
    <Compile Include="Common\RefundList.cs" />
    <Compile Include="Common\IEmailSenderGateway.cs" />
    <Compile Include="Common\TransactionList.cs" />
    <Compile Include="CustomEntity\CustomsDeclarationMatchingInput.cs" />
    <Compile Include="CustomEntity\ListContRegistedDeclarationOrCargoId.cs" />
    <Compile Include="IAuthGateway.cs" />
    <Compile Include="IBillingGateway.cs" />
    <Compile Include="ICategoryGateway.cs" />
    <Compile Include="IAnonymousGateway.cs" />
    <Compile Include="ICustomsGateway.cs" />
    <Compile Include="IEportBillingGateway.cs" />
    <Compile Include="IConfigGateway.cs" />
    <Compile Include="IContInfoGateway.cs" />
    <Compile Include="Intf\IEPort2PSafeGateway.cs" />
    <Compile Include="Intf\IEPortCustomClearanceGateway.cs" />
    <Compile Include="IRegistrationGateway.cs" />
    <Compile Include="ISmsGateway.cs" />
    <Compile Include="Intf\IOrderIntfGateway.cs" />
    <Compile Include="IOrderGateway.cs" />
    <Compile Include="IContMovementsGateway.cs" />
    <Compile Include="ISnpTransactionGateway.cs" />
    <Compile Include="ITOSGateway.cs" />
    <Compile Include="ITOSCategoryGateway.cs" />
    <Compile Include="ITOSContainerGateway.cs" />
    <Compile Include="ITOSOperationGateway.cs" />
    <Compile Include="ITOSVesselGateway.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="AccompaniedService\IAccompaniedServiceGateway.cs" />
    <Compile Include="Transport\ITransportGateway.cs" />
    <Compile Include="IMSDSGateway.cs" />
    <Compile Include="Models\MSDSModels.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Snp.ePort.Common\Snp.ePort.Common.csproj">
      <Project>{f58832de-5553-42c9-8566-8120535248a8}</Project>
      <Name>Snp.ePort.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Entity\Snp.ePort.Entity.csproj">
      <Project>{910c50b0-22e2-415c-97de-435bb67a47e1}</Project>
      <Name>Snp.ePort.Entity</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.ePort.Service.Impl\Snp.ePort.Service.Impl.csproj">
      <Project>{56c48660-91ff-488d-b216-f5e3c191561f}</Project>
      <Name>Snp.ePort.Service.Impl</Name>
    </ProjectReference>
    <ProjectReference Include="..\Snp.Tos.Entity\Snp.Tos.Entity.csproj">
      <Project>{62d4bfe6-7de9-44ec-a16d-f15a2a894235}</Project>
      <Name>Snp.Tos.Entity</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="Properties\DataSources\Snp.ePort.Entity.CHARGE.datasource" />
    <None Include="Snp.ePort.Gateway.ruleset" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>