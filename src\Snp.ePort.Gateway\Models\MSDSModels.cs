using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Web;
using System.Web;

namespace Snp.ePort.Gateway.Models
{
    public class MSDSUploadRequest
    {
        [Required(ErrorMessage = "OrderDetailNos is required")]
        public List<string> OrderDetailNos { get; set; }

        [Required(ErrorMessage = "Files is required")]
        public List<HttpPostedFile> Files { get; set; }

        public MSDSUploadRequest()
        {
            OrderDetailNos = new List<string>();
            Files = new List<HttpPostedFile>();
        }
    }

    public class MSDSUploadResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<MSDSUploadedFileInfo> UploadedFiles { get; set; }

        public MSDSUploadResponse()
        {
            UploadedFiles = new List<MSDSUploadedFileInfo>();
        }
    }

    public class MSDSUploadedFileInfo
    {
        public string FileId { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public List<string> LinkedOrderDetails { get; set; }

        public MSDSUploadedFileInfo()
        {
            LinkedOrderDetails = new List<string>();
        }
    }

    public class MSDSDeleteResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
    }

    public class MSDSFileInfo
    {
        public string FileId { get; set; }
        public string FileName { get; set; }
        public string FilePath { get; set; }
        public long FileSize { get; set; }
        public string OrderDetailNo { get; set; }
        public System.DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
    }

    public class MSDSConfigInfo
    {
        public int MaxFilesPerContainer { get; set; }
        public long MaxTotalSizePerContainer { get; set; }
        public List<string> AllowedExtensions { get; set; }

        public MSDSConfigInfo()
        {
            AllowedExtensions = new List<string>();
        }
    }
}
